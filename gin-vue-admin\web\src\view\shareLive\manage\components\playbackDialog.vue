<template>
    <el-dialog
            title="直播回放"
            :visible="isShow"
            width="700px"
            :before-close="handleClose">
        <el-form :model="formData" label-width="90px">
            <el-form-item label="回放状态:">
                <el-radio-group v-model="formData.is_playback" @change="handleIsPlayBackChange">
                    <el-radio :label="1">是</el-radio>
                    <el-radio :label="2">否</el-radio>
                </el-radio-group>
            </el-form-item>
        </el-form>
        <el-table :data="list" class="mt25">
            <el-table-column label="创建时间" align="center">
                <template slot-scope="scope">
                    {{ scope.row.created_at | formatDate }}
                </template>
            </el-table-column>
            <!--            <el-table-column label="视频" width="300" align="center">
                            <template slot-scope="scope">
                                <video :src="scope.row.video_url" controls width="300px" height="200px"></video>
                            </template>
                        </el-table-column>-->
            <el-table-column label="视频格式" prop="file_format" align="center">
            </el-table-column>
            <el-table-column label="操作" align="center">
                <template slot-scope="scope">
                    <el-button type="text" @click="openPlayDialog(scope.$index)">查看回放</el-button>
                </template>
            </el-table-column>
        </el-table>
        <div slot="footer" class="dialog-footer">
            <el-button @click="handleClose">关 闭</el-button>
        </div>
        <el-dialog
                title="查看回放"
                :visible="playIsShow"
                width="900px"
                :modal-append-to-body="false"
                :append-to-body="true"
                :before-close="playBackClose">
<!--            <video :src="playUrl" controls width="100%" height="100%"></video>-->
            <div>
                <video v-if="playIsShow && !isMp4" ref="videoPlayer" class="video-js vjs-default-skin vjs-big-play-centered video" preload="auto" controls>
                    <source :src="playUrl" type="application/x-mpegURL"/>
                </video>
                <video v-else-if="playIsShow && isMp4" ref="mp4Player" class="video" preload="auto" controls :src="playUrl"></video>
            </div>

            <div slot="footer" class="dialog-footer">
                <el-button @click="playBackClose">关 闭</el-button>
            </div>
        </el-dialog>
    </el-dialog>
</template>
<script>
import {getShareLiveRoomRecordFileByRoomId, SaveShareLiveRoomIsPlayBack} from "@/api/shareLive";
import videojs from 'video.js'
import 'video.js/dist/video-js.css'
import 'videojs-contrib-hls'
export default {
    data() {
        return {
            isMp4:false,
            isShow: false,
            list: [],
            playUrl: "",
            playIsShow: false,
            formData: {
                id: null,
                is_playback: 2,
            },
            videoJs:{}
        }
    },
    methods: {
        playBackClose(){
            // 清理 Video.js 实例 (用于 HLS 视频)
            if (this.videoJs && typeof this.videoJs.dispose === 'function') {
                this.videoJs.dispose();
            }
            this.videoJs = {};

            // 清理原生 MP4 视频播放器
            if (this.isMp4 && this.$refs.mp4Player) {
                const mp4Video = this.$refs.mp4Player;
                mp4Video.pause(); // 暂停播放
                mp4Video.currentTime = 0; // 重置播放进度
                mp4Video.src = ''; // 清空视频源
                mp4Video.load(); // 重新加载（清空缓冲区）
            }

            this.playIsShow = false;
            this.playUrl = '';
            this.isMp4 = false;
        },
        async handleIsPlayBackChange(val) {
            let params = {
                id: this.formData.id,
                is_playback: this.formData.is_playback
            }
            const {code, msg} = await SaveShareLiveRoomIsPlayBack(params)
            if (code === 0) {
                this.$message.success(msg)
            }
        },
        init(id, is_playback) {
            this.isShow = true
            this.getList(id)
            this.formData.id = id
            this.formData.is_playback = is_playback
        },
        // 获取列表
        async getList(id) {
            const {code, data} = await getShareLiveRoomRecordFileByRoomId({share_live_room_id: id})
            if (code === 0) {
                this.list = data
                console.log(data);
            }
        },
        handleClose() {
            this.isShow = false
            this.list = []
            this.formData = {
                id: null,
                is_playback: 2,
            }
            this.$emit('reLoad')
        },
        openPlayDialog(index) {
            this.playUrl = this.list[index].video_url;
            this.isMp4 = this.playUrl.toLowerCase().endsWith('.mp4');
            this.playIsShow = true
            if(this.isMp4){
                return;
            }
            setTimeout(()=>{
                this.videoJs = videojs(this.$refs.videoPlayer,{
                    "controls": true,
                    "autoplay": false,
                    playbackRates: [0.5, 1, 1.5, 2]
                });
            },500)
        }
    }
}
</script>
<style scoped lang="scss">
.video {
    width: 100%;
    max-height: 500px;
}
</style>